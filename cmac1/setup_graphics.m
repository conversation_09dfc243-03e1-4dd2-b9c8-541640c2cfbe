figure(1)
colordef none
clf reset
whitebg(gcf,[0 0 0])
set(gcf,'Units','normalized','Name','CMAC Simulation')

ax_mem = axes('Position',[0.05 0.52 0.40 0.45]);
if bucket_mode == 1
  h_mem = surf(0:63,0:63,zeros(64));
  view([0 90])
  axis([0 63 0 63]), axis off, axis equal
  caxis([-0.01,0.01])
else
  % For sequential mode, show the full CMAC memory just like random mode
  h_mem = surf(0:63,0:63,zeros(64));
  view([0 90])
  axis([0 63 0 63]), axis off, axis equal
  caxis([-0.01,0.01])
end
colormap jet

xcoords = 0:359;
ycoords = sin(xcoords*pi/180);

ax_out = axes('Position',[0.55 0.5 0.40 0.45]);
hold on
h_desired = plot(xcoords,ycoords,':','Color','c');
h_out = plot(xcoords,0*xcoords,'Color','w');
axis([0 360 -1.3 1.3])
axis off

ax_in = axes('Position',[0.15 0.15 0.75 0.32]);
hold on
h_in = plot(xcoords,ycoords,'g');
axis([0 360 -1.2 1.2])
box off
set(ax_in,'Xtick',[0 90 180 270 360])
set(ax_in,'ButtonDownFcn','clickhandler')
set(h_in,'ButtonDownFcn','clickhandler')
title('Click on the green curve to sample data points','Color','g')

reset_button = ...
    uicontrol('Style','Pushbutton', ...
	      'Units','Pixels', 'Position',[3 3 60 20], ...
	      'BackgroundColor',[1 0.6 0.6],'String','Reset', ...
	      'Callback','reset_cmac');

sample_button = ...
    uicontrol('Style','Pushbutton', ...
	      'Units','Pixels', 'Position',[70 3 60 20], ...
	      'BackgroundColor',[1 1 0],'String','Sample', ...
	      'CallBack','sample_point;plot_cmac');

sample_10_button = ...
    uicontrol('Style','Pushbutton', ...
	      'Units','Pixels', 'Position',[140 3 60 20], ...
	      'BackgroundColor',[1 1 0],'String','x 10', ...
	      'CallBack','for i=1:10,sample_point,end,plot_cmac');

function_menu = ...
    uicontrol('Style','Popup', ...
	      'Units','Pixels', 'Position',[220 3 80 20], ...
	      'BackgroundColor',[0.2 1 0.2], ...
	      'String',{'sin(x)','sin(3x)','sin(8x)','steps','random'}, ...
	      'CallBack','setup_data_points');

train_button = ...
    uicontrol('Style','Pushbutton', ...
	      'Units','Pixels', 'Position',[310 3 70 20], ...
	      'BackgroundColor',[1 1 0],'String','Rehearse', ...
	      'CallBack','for i=1:size(training_points,1),pcoords=training_points(i,:);train_point,end,plot_cmac');

bucketmode_menu = ...
    uicontrol('Style','Popup', ...
	      'Units','Pixels', 'Position',[390 3 80 20], ...
	      'BackgroundColor',[0.2 1 0.2], ...
	      'String',{'Rand','Seq'}, ...
	      'Value',bucket_mode, ...
	      'CallBack','set_bucketmode');

% --- Define a bottom margin for this new row of controls ---
param_row_y = 33;

% --- N Hashes ---
uicontrol('Style','text', 'Position',[10 param_row_y 60 20], ...
          'BackgroundColor',[0 0 0], 'ForegroundColor',[0.8 0.8 1], ...
          'String','N Hashes:');
uicontrol('Style','edit', 'Position',[75 param_row_y 40 20], ...
          'String',num2str(Nhashes), ...
          'BackgroundColor',[0.8 0.8 1], ...
          'Tag','nhashes_box', ...
          'Callback','update_cmac_params');

% --- Buckets/Hash ---
uicontrol('Style','text', 'Position',[125 param_row_y 85 20], ...
          'BackgroundColor',[0 0 0], 'ForegroundColor',[0.8 0.8 1], ...
          'String','N Buckets/Hash:');
uicontrol('Style','edit', 'Position',[215 param_row_y 40 20], ...
          'String',num2str(Nbuckets_per_hash), ...
          'BackgroundColor',[0.8 0.8 1], ...
          'Tag','nbuckets_box', ...
          'Callback','update_cmac_params');

% --- Hash Stride ---
uicontrol('Style','text', 'Position',[265 param_row_y 70 20], ...
          'BackgroundColor',[0 0 0], 'ForegroundColor',[0.8 0.8 1], ...
          'String','Hash Stride(°):');
uicontrol('Style','edit', 'Position',[340 param_row_y 40 20], ...
          'String',num2str(hash_stride), ...
          'BackgroundColor',[0.8 0.8 1], ...
          'Tag','hashstride_box', ...
          'Callback','update_cmac_params');

% --- Learning Rate (Re-created and moved for horizontal alignment) ---
g_text = uicontrol('Style','Text', ...
           'Units','Pixels', 'Position',[390 param_row_y 80 20], ...
           'BackgroundColor',[0 0 0], 'ForegroundColor',[0.8 0.8 1], ...
           'String','Learning rate:');
g_valbox = uicontrol('Style','Edit', ...
           'Units','Pixels', 'Position',[475 param_row_y 50 20], ...
           'BackgroundColor',[0.8 0.8 1], ...
           'Value',1, 'String','1', ...
           'CallBack','set_g_value');
set_g_value
